import requests
from bs4 import BeautifulSoup
import time

def test_with_simple_headers():
    session = requests.Session()
    
    # 使用简单的headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    }
    session.headers.update(headers)
    
    # 先访问主页
    try:
        print("访问主页获取cookies...")
        session.get('https://b.faloo.com/', timeout=10)
        time.sleep(1)
    except Exception as e:
        print(f"访问主页失败: {e}")
    
    # 访问小说页面
    url = "https://b.faloo.com/1236995.html"
    print(f"访问小说页面: {url}")
    
    try:
        response = session.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        
        # 检查是否是反爬虫页面
        if 'script' in response.text[:100].lower() and len(response.text) < 1000:
            print("检测到反爬虫页面")
            return False
        
        response.encoding = 'gbk'
        soup = BeautifulSoup(response.text, 'html.parser')
        
        title = soup.title.text if soup.title else '无标题'
        print(f"页面标题: {title}")
        
        links = soup.find_all('a', href=True)
        print(f"链接数量: {len(links)}")
        
        # 查找章节链接
        chapter_count = 0
        for link in links:
            text = link.text.strip()
            href = link['href']
            if '1236995_' in href and text and '第' in text and '章' in text:
                chapter_count += 1
                if chapter_count <= 5:
                    print(f"章节: {text} -> {href}")
        
        print(f"找到章节数: {chapter_count}")
        return True
        
    except Exception as e:
        print(f"访问失败: {e}")
        return False

if __name__ == "__main__":
    test_with_simple_headers()
