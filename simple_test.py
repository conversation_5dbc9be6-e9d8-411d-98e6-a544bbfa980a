import requests
from bs4 import BeautifulSoup

url = "https://b.faloo.com/1236995.html"

# 尝试不同的请求方式
print("测试1: 基本请求")
try:
    response = requests.get(url)
    print(f"状态码: {response.status_code}")
    print(f"内容长度: {len(response.text)}")
    print(f"前200字符: {response.text[:200]}")
except Exception as e:
    print(f"错误: {e}")

print("\n" + "="*50)

print("测试2: 带headers的请求")
try:
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://b.faloo.com/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }
    response = requests.get(url, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"内容长度: {len(response.text)}")
    print(f"前200字符: {response.text[:200]}")
    
    # 尝试不同编码
    response.encoding = 'gbk'
    soup = BeautifulSoup(response.text, 'html.parser')
    print(f"标题: {soup.title.text if soup.title else '无标题'}")
    
    links = soup.find_all('a', href=True)
    print(f"链接数量: {len(links)}")
    
except Exception as e:
    print(f"错误: {e}")
