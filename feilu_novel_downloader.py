import requests
from bs4 import BeautifulSoup
import time
import os
import re
import json
from urllib.parse import urljoin
import random
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('feilu_downloader.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

class FeiluNovelDownloader:
    def __init__(self, username=None, password=None):
        """
        初始化下载器
        :param username: 飞卢账号用户名
        :param password: 飞卢账号密码
        """
        self.session = requests.Session()
        # 使用更简单的headers避免反爬虫检测
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        }
        self.session.headers.update(self.headers)
        self.username = username
        self.password = password
        self.is_logged_in = False
        self.novel_info = {}

        # 先访问主页获取cookies
        try:
            self.session.get('https://b.faloo.com/', timeout=10)
        except:
            pass
        
    def login(self):
        """
        登录飞卢网站
        """
        if not self.username or not self.password:
            logger.warning("没有提供账号信息，将以游客身份访问，部分付费章节可能无法获取")
            return False
            
        login_url = "https://u.faloo.com/regist/Login.aspx"
        try:
            # 获取登录页面
            response = self.session.get(login_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取登录表单所需的参数
            viewstate = soup.select_one('#__VIEWSTATE')['value'] if soup.select_one('#__VIEWSTATE') else ''
            event_validation = soup.select_one('#__EVENTVALIDATION')['value'] if soup.select_one('#__EVENTVALIDATION') else ''
            
            # 构建登录数据
            login_data = {
                '__VIEWSTATE': viewstate,
                '__EVENTVALIDATION': event_validation,
                'txtUserName': self.username,
                'txtPassword': self.password,
                'btnLogin': '登录',
                'hidGotoUrl': '',
                'hidIsFromLogin': '1'
            }
            
            # 发送登录请求
            response = self.session.post(login_url, data=login_data)
            
            # 检查登录状态
            if '登录成功' in response.text or self.username in response.text:
                logger.info("登录成功！")
                self.is_logged_in = True
                return True
            else:
                logger.error("登录失败，请检查账号密码是否正确")
                return False
                
        except Exception as e:
            logger.error(f"登录过程中出现错误: {str(e)}")
            return False
    
    def get_novel_info(self, novel_url):
        """
        获取小说基本信息
        :param novel_url: 小说首页链接
        """
        try:
            response = self.session.get(novel_url)

            # 尝试不同的编码，飞卢网站使用GBK编码
            encodings = ['gbk', 'gb2312', 'utf-8']
            soup = None
            for encoding in encodings:
                try:
                    response.encoding = encoding
                    soup = BeautifulSoup(response.text, 'html.parser')
                    title_test = soup.title.text if soup.title else ''
                    if '?' not in title_test and '�' not in title_test:  # 如果没有乱码
                        break
                except:
                    continue

            if not soup:
                response.encoding = 'gbk'  # 默认使用GBK
                soup = BeautifulSoup(response.text, 'html.parser')

            # 尝试多种可能的标题选择器
            title_selectors = [
                '.ni_title h1',
                '.book-title h1',
                '.title h1',
                'h1.title',
                '.book-info h1',
                'h1',
                '.novel-title'
            ]

            title = '未知标题'
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem and title_elem.text.strip():
                    title = title_elem.text.strip()
                    break

            # 尝试多种可能的作者选择器
            author_selectors = [
                '.ni_au a',
                '.book-author a',
                '.author a',
                '.book-info .author',
                '.novel-author'
            ]

            author = '未知作者'
            for selector in author_selectors:
                author_elem = soup.select_one(selector)
                if author_elem and author_elem.text.strip():
                    author = author_elem.text.strip()
                    break

            # 尝试多种可能的封面选择器
            cover_selectors = [
                '.ni_img img',
                '.book-cover img',
                '.cover img',
                '.book-info img'
            ]

            cover_url = None
            for selector in cover_selectors:
                cover_elem = soup.select_one(selector)
                if cover_elem and 'src' in cover_elem.attrs:
                    cover_url = cover_elem['src']
                    break

            # 尝试多种可能的简介选择器
            intro_selectors = [
                '.ni_intro',
                '.book-intro',
                '.intro',
                '.book-desc',
                '.description',
                '.novel-intro'
            ]

            intro = '暂无简介'
            for selector in intro_selectors:
                intro_elem = soup.select_one(selector)
                if intro_elem and intro_elem.text.strip():
                    intro = intro_elem.text.strip()
                    break

            self.novel_info = {
                'title': title,
                'author': author,
                'cover_url': cover_url,
                'intro': intro,
                'url': novel_url
            }

            logger.info(f"已获取小说信息: {title} - {author}")
            return self.novel_info

        except Exception as e:
            logger.error(f"获取小说信息时出错: {str(e)}")
            return None
    
    def get_chapter_list(self, novel_url):
        """
        获取小说章节列表
        :param novel_url: 小说首页或目录页链接
        """
        try:
            # 首先尝试从小说主页获取章节列表
            response = self.session.get(novel_url)

            # 使用正确的编码
            encodings = ['gbk', 'gb2312', 'utf-8']
            soup = None
            for encoding in encodings:
                try:
                    response.encoding = encoding
                    soup = BeautifulSoup(response.text, 'html.parser')
                    title_test = soup.title.text if soup.title else ''
                    if '?' not in title_test and '�' not in title_test:  # 如果没有乱码
                        break
                except:
                    continue

            if not soup:
                response.encoding = 'gbk'  # 默认使用GBK
                soup = BeautifulSoup(response.text, 'html.parser')

            chapter_links = []

            # 首先尝试在主页面查找章节链接
            all_links = soup.find_all('a', href=True)
            novel_id = novel_url.split('/')[-1].split('.')[0]  # 提取小说ID

            for link in all_links:
                href = link['href']
                text = link.text.strip()

                # 更精确的章节链接匹配
                is_chapter_link = False

                # 方法1: 检查href是否符合章节URL模式 (novel_id_chapter_number.html)
                if novel_id in href and '_' in href and '.html' in href:
                    # 检查是否是 novel_id_数字.html 的格式
                    pattern = rf'{novel_id}_(\d+)\.html'
                    if re.search(pattern, href):
                        is_chapter_link = True

                # 方法2: 检查文本是否包含明确的章节标识
                if text and re.search(r'第\d+章', text):
                    is_chapter_link = True

                if is_chapter_link:
                    # 过滤掉明显不是章节的链接
                    skip_keywords = [
                        '目录', '返回', '更多', '作者', '书评', '推荐', '收藏',
                        '点击阅读', '我要订阅', '下载', '作品相关', '感言',
                        '上一章', '下一章', '章节目录', '加入书签', '第一卷', '第二卷',
                        '第三卷', '第四卷', '第五卷', '第六卷', '第七卷', '第八卷', '第九卷',
                        '承诺书', '卷', '初始', '厄难使者', '灾厄时代', '深渊魔境'
                    ]

                    # 额外检查：如果文本只包含卷信息而没有具体章节号，跳过
                    if '卷' in text and '第' in text and '章' not in text:
                        continue

                    if not any(skip in text for skip in skip_keywords) and len(text) > 2:
                        chapter_url = urljoin('https://b.faloo.com', href)

                        # 判断是否为付费章节
                        is_vip = '(VIP)' in text or '[VIP]' in text or link.get('class') and 'vip' in ' '.join(link.get('class', []))

                        chapter_links.append({
                            'title': text,
                            'url': chapter_url,
                            'is_vip': is_vip
                        })

            # 如果在主页面没找到足够的章节，尝试目录页
            if len(chapter_links) < 10:
                # 查找目录链接
                catalog_link = None
                for link in all_links:
                    text = link.text.strip()
                    if any(keyword in text for keyword in ['目录', '章节目录', '全部章节']):
                        catalog_link = urljoin('https://b.faloo.com', link['href'])
                        break

                # 如果找到目录链接，访问目录页
                if catalog_link:
                    logger.info(f"访问目录页: {catalog_link}")
                    catalog_response = self.session.get(catalog_link)
                    catalog_response.encoding = response.encoding
                    catalog_soup = BeautifulSoup(catalog_response.text, 'html.parser')

                    # 在目录页查找章节
                    catalog_links = catalog_soup.find_all('a', href=True)
                    for link in catalog_links:
                        href = link['href']
                        text = link.text.strip()

                        if text and any(keyword in text for keyword in ['第', '章', 'Chapter', '序', '楔子', '前言']):
                            chapter_url = urljoin('https://b.faloo.com', href)
                            is_vip = '(VIP)' in text or '[VIP]' in text

                            chapter_links.append({
                                'title': text,
                                'url': chapter_url,
                                'is_vip': is_vip
                            })

            # 去重并排序
            seen_urls = set()
            unique_chapters = []
            for chapter in chapter_links:
                if chapter['url'] not in seen_urls:
                    seen_urls.add(chapter['url'])
                    unique_chapters.append(chapter)

            # 尝试按章节号排序
            def extract_chapter_number(title):
                match = re.search(r'第(\d+)章', title)
                return int(match.group(1)) if match else 0

            try:
                unique_chapters.sort(key=lambda x: extract_chapter_number(x['title']))
            except:
                pass  # 如果排序失败，保持原顺序

            logger.info(f"总共获取到 {len(unique_chapters)} 个章节")
            return unique_chapters

        except Exception as e:
            logger.error(f"获取章节列表时出错: {str(e)}")
            return []
    
    def get_chapter_content(self, chapter_url, is_vip=False, retry=3):
        """
        获取章节内容
        :param chapter_url: 章节URL
        :param is_vip: 是否为VIP章节
        :param retry: 重试次数
        """
        if is_vip and not self.is_logged_in:
            logger.warning("需要登录才能获取VIP章节内容")
            return "【需要登录才能获取VIP章节内容】"
        
        for i in range(retry):
            try:
                # 添加随机延迟，避免请求过于频繁
                time.sleep(random.uniform(1, 3))
                
                response = self.session.get(chapter_url)

                # 使用正确的编码
                encodings = ['gbk', 'gb2312', 'utf-8']
                soup = None
                for encoding in encodings:
                    try:
                        response.encoding = encoding
                        soup = BeautifulSoup(response.text, 'html.parser')
                        # 简单测试编码是否正确
                        test_text = soup.get_text()[:100]
                        if '?' not in test_text and '�' not in test_text:
                            break
                    except:
                        continue

                if not soup:
                    response.encoding = 'gbk'  # 默认使用GBK
                    soup = BeautifulSoup(response.text, 'html.parser')
                
                # 检查是否为付费章节 - 扩展检测条件
                vip_indicators = [
                    "您还没有登录，请登录后在继续阅读",
                    "购买本章",
                    "订阅本章",
                    "升级VIP会员",
                    "立即登录",
                    "需要订阅",
                    "充30元即可升级VIP会员"
                ]

                is_vip_chapter = any(indicator in response.text for indicator in vip_indicators)

                if is_vip_chapter:
                    if not is_vip:
                        logger.warning(f"章节 {chapter_url} 为付费章节，但未标记为VIP")
                    
                    if not self.is_logged_in:
                        logger.info(f"发现付费章节: {chapter_url}")
                        return "【付费章节，需要登录后下载】\n\n本章节需要VIP会员或付费订阅才能阅读。\n如需下载付费章节，请使用有效的飞卢账号登录。"
                    
                    # 尝试获取付费章节内容
                    # 这里需要实现获取付费章节的逻辑，可能涉及到购买或使用特殊API
                    # 由于涉及付费，此处仅作示例，不实际实现
                    logger.info("尝试获取付费章节内容")
                    
                    # 从页面获取chapter_id和novel_id
                    chapter_id_match = re.search(r'var readChapterId\s*=\s*(\d+)', response.text)
                    novel_id_match = re.search(r'var novelId\s*=\s*(\d+)', response.text)
                    
                    if chapter_id_match and novel_id_match:
                        chapter_id = chapter_id_match.group(1)
                        novel_id = novel_id_match.group(1)
                        
                        # 构建API请求
                        api_url = f"https://b.faloo.com/ajax/ReadChapter.aspx"
                        params = {
                            "nid": novel_id,
                            "cid": chapter_id,
                            "rid": "0"
                        }
                        
                        api_response = self.session.get(api_url, params=params)
                        try:
                            json_data = json.loads(api_response.text)
                            if json_data.get('code') == 0 and json_data.get('data'):
                                content_html = json_data['data'].get('content', '')
                                soup_content = BeautifulSoup(content_html, 'html.parser')
                                return soup_content.get_text('\n\n')
                        except:
                            pass
                    
                    return "【付费章节，无法获取内容】\n\n此章节需要付费订阅，且当前登录状态无法获取内容。"
                
                # 一般章节内容获取 - 尝试多种选择器
                content_selectors = [
                    '#center',  # 飞卢网站的主要内容区域
                    '.nr_center',
                    '#content',
                    '.content',
                    '#chaptercontent',
                    '.chapter-content',
                    '.read-content',
                    '.txt'
                ]

                content_div = None
                for selector in content_selectors:
                    content_div = soup.select_one(selector)
                    if content_div:
                        break

                if content_div:
                    # 去除广告和不需要的元素
                    for ad in content_div.select('.bd_ad, .ad, .advertisement'):
                        ad.decompose()

                    # 去除导航和其他非内容元素
                    for nav in content_div.select('.nav, .navigation, .breadcrumb, .header, .footer'):
                        nav.decompose()

                    content = content_div.get_text('\n\n')
                    # 清理文本
                    content = re.sub(r'\n{3,}', '\n\n', content)
                    content = content.strip()

                    # 进一步清理，只保留章节内容部分
                    lines = content.split('\n')
                    content_lines = []
                    start_content = False

                    for line in lines:
                        line = line.strip()
                        # 跳过空行
                        if not line:
                            if start_content:
                                content_lines.append('')
                            continue

                        # 跳过明显的元数据和导航信息
                        skip_patterns = [
                            '小说', '作者', '更新时间', '阅读', '听书', '收藏', '推荐',
                            '瀑布', '从本章开始', '同人', '动漫', '妖神级皮卡丘',
                            '综漫：从零开始组建神群', '|', '第.*章.*第.*章'  # 重复的章节标题
                        ]

                        should_skip = False
                        for pattern in skip_patterns:
                            if pattern in line:
                                should_skip = True
                                break

                        if should_skip:
                            continue

                        # 检测内容开始的标志 - 寻找实际的故事内容
                        if not start_content and (
                            len(line) > 15 and
                            any(keyword in line for keyword in ['罗枢', '睁开', '眼眸', '茫然', '看着']) or
                            (len(line) > 30 and not any(meta in line for meta in ['章', '卷', '时间', '作者']))
                        ):
                            start_content = True

                        if start_content:
                            content_lines.append(line)

                    final_content = '\n'.join(content_lines).strip()
                    if len(final_content) > 50:  # 确保有足够的内容
                        return final_content
                
                logger.warning(f"未能从章节页面获取内容，将重试 ({i+1}/{retry})")
            
            except Exception as e:
                logger.error(f"获取章节内容时出错: {str(e)}, 将重试 ({i+1}/{retry})")
        
        return "【获取章节内容失败】"
    
    def download_novel(self, novel_url, output_dir=None, start_chapter=1, end_chapter=None):
        """
        下载小说
        :param novel_url: 小说URL
        :param output_dir: 输出目录
        :param start_chapter: 起始章节索引（从1开始）
        :param end_chapter: 结束章节索引（如果为None则下载全部）
        """
        # 获取小说信息
        novel_info = self.get_novel_info(novel_url)
        if not novel_info:
            logger.error("无法获取小说信息，下载失败")
            return False
        
        # 获取章节列表
        chapters = self.get_chapter_list(novel_url)
        if not chapters:
            logger.error("无法获取章节列表，下载失败")
            return False
        
        # 创建输出目录
        novel_title = novel_info['title']
        if not output_dir:
            output_dir = f"./novels/{novel_title}"
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存小说信息
        with open(os.path.join(output_dir, 'info.json'), 'w', encoding='utf-8') as f:
            json.dump(novel_info, f, ensure_ascii=False, indent=4)
        
        # 下载小说封面
        if novel_info['cover_url']:
            try:
                cover_response = self.session.get(novel_info['cover_url'])
                with open(os.path.join(output_dir, 'cover.jpg'), 'wb') as f:
                    f.write(cover_response.content)
                logger.info("已下载小说封面")
            except Exception as e:
                logger.error(f"下载封面时出错: {str(e)}")
        
        # 调整章节范围
        if start_chapter < 1:
            start_chapter = 1
        
        if end_chapter is None or end_chapter > len(chapters):
            end_chapter = len(chapters)
        
        # 创建小说内容文件
        txt_filename = os.path.join(output_dir, f"{novel_title}.txt")
        with open(txt_filename, 'w', encoding='utf-8') as txt_file:
            # 写入小说信息
            txt_file.write(f"{novel_title}\n")
            txt_file.write(f"作者: {novel_info['author']}\n\n")
            txt_file.write(f"简介:\n{novel_info['intro']}\n\n")
            txt_file.write("-" * 50 + "\n\n")
            
            # 下载每个章节
            total_chapters = end_chapter - start_chapter + 1
            for i, chapter in enumerate(chapters[start_chapter-1:end_chapter], 1):
                chapter_title = chapter['title']
                chapter_url = chapter['url']
                is_vip = chapter['is_vip']
                
                logger.info(f"正在下载: [{i}/{total_chapters}] {chapter_title}")
                
                # 获取章节内容
                content = self.get_chapter_content(chapter_url, is_vip)
                
                # 写入章节到文件
                txt_file.write(f"{chapter_title}\n\n")
                txt_file.write(f"{content}\n\n")
                txt_file.write("-" * 30 + "\n\n")
                
                # 每下载10章保存一次
                if i % 10 == 0:
                    txt_file.flush()
                    logger.info(f"已完成: {i}/{total_chapters} 章节")
        
        logger.info(f"小说《{novel_title}》下载完成！保存路径: {txt_filename}")
        return True


if __name__ == "__main__":
    # 使用示例
    print("飞卢小说下载器")
    print("=" * 50)
    
    # 输入账号密码（可选）
    use_account = input("是否使用账号登录下载付费内容？(y/n): ").strip().lower()
    username = None
    password = None
    
    if use_account == 'y':
        username = input("请输入账号: ").strip()
        password = input("请输入密码: ").strip()
    
    # 创建下载器
    downloader = FeiluNovelDownloader(username, password)
    
    # 如果提供了账号密码，尝试登录
    if username and password:
        if not downloader.login():
            print("登录失败，将以游客身份继续")
    
    # 输入小说网址
    novel_url = input("请输入小说链接(例如:https://b.faloo.com/novel/123456.html): ").strip()
    
    # 章节范围（可选）
    range_option = input("下载全部章节？(y/n, 默认y): ").strip().lower()
    start_chapter = 1
    end_chapter = None
    
    if range_option == 'n':
        try:
            start_input = input("请输入起始章节序号(默认1): ").strip()
            start_chapter = int(start_input) if start_input else 1
            
            end_input = input("请输入结束章节序号(留空表示到最后): ").strip()
            end_chapter = int(end_input) if end_input else None
        except ValueError:
            print("输入格式错误，将使用默认值")
            start_chapter = 1
            end_chapter = None
    
    # 输出目录（可选）
    output_dir = input("请输入保存目录(留空使用默认目录): ").strip()
    
    print("\n开始下载小说...")
    downloader.download_novel(novel_url, output_dir, start_chapter, end_chapter) 