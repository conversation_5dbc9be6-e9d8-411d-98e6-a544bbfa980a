import requests
from bs4 import BeautifulSoup
import time
import os
import re
import json
from urllib.parse import urljoin
import random
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('feilu_downloader.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

class FeiluNovelDownloader:
    def __init__(self, username=None, password=None):
        """
        初始化下载器
        :param username: 飞卢账号用户名
        :param password: 飞卢账号密码
        """
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://b.faloo.com/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        self.session.headers.update(self.headers)
        self.username = username
        self.password = password
        self.is_logged_in = False
        self.novel_info = {}
        
    def login(self):
        """
        登录飞卢网站
        """
        if not self.username or not self.password:
            logger.warning("没有提供账号信息，将以游客身份访问，部分付费章节可能无法获取")
            return False
            
        login_url = "https://u.faloo.com/regist/Login.aspx"
        try:
            # 获取登录页面
            response = self.session.get(login_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取登录表单所需的参数
            viewstate = soup.select_one('#__VIEWSTATE')['value'] if soup.select_one('#__VIEWSTATE') else ''
            event_validation = soup.select_one('#__EVENTVALIDATION')['value'] if soup.select_one('#__EVENTVALIDATION') else ''
            
            # 构建登录数据
            login_data = {
                '__VIEWSTATE': viewstate,
                '__EVENTVALIDATION': event_validation,
                'txtUserName': self.username,
                'txtPassword': self.password,
                'btnLogin': '登录',
                'hidGotoUrl': '',
                'hidIsFromLogin': '1'
            }
            
            # 发送登录请求
            response = self.session.post(login_url, data=login_data)
            
            # 检查登录状态
            if '登录成功' in response.text or self.username in response.text:
                logger.info("登录成功！")
                self.is_logged_in = True
                return True
            else:
                logger.error("登录失败，请检查账号密码是否正确")
                return False
                
        except Exception as e:
            logger.error(f"登录过程中出现错误: {str(e)}")
            return False
    
    def get_novel_info(self, novel_url):
        """
        获取小说基本信息
        :param novel_url: 小说首页链接
        """
        try:
            response = self.session.get(novel_url)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 获取小说标题
            title_elem = soup.select_one('.ni_title h1')
            title = title_elem.text.strip() if title_elem else '未知标题'
            
            # 获取作者名
            author_elem = soup.select_one('.ni_au a')
            author = author_elem.text.strip() if author_elem else '未知作者'
            
            # 获取封面图
            cover_elem = soup.select_one('.ni_img img')
            cover_url = cover_elem['src'] if cover_elem and 'src' in cover_elem.attrs else None
            
            # 获取简介
            intro_elem = soup.select_one('.ni_intro')
            intro = intro_elem.text.strip() if intro_elem else '暂无简介'
            
            self.novel_info = {
                'title': title,
                'author': author,
                'cover_url': cover_url,
                'intro': intro,
                'url': novel_url
            }
            
            logger.info(f"已获取小说信息: {title} - {author}")
            return self.novel_info
            
        except Exception as e:
            logger.error(f"获取小说信息时出错: {str(e)}")
            return None
    
    def get_chapter_list(self, novel_url):
        """
        获取小说章节列表
        :param novel_url: 小说首页或目录页链接
        """
        try:
            # 构建目录页URL
            if 'b.faloo.com' in novel_url and '/novel/' in novel_url:
                # 如果是小说首页，转换为目录页
                novel_id = re.search(r'/(\d+)\.html', novel_url)
                if novel_id:
                    catalog_url = f"https://b.faloo.com/novel/{novel_id.group(1)}/catalog.html"
                else:
                    catalog_url = novel_url.replace('.html', '/catalog.html')
            else:
                catalog_url = novel_url
            
            response = self.session.get(catalog_url)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 获取所有章节链接
            chapter_links = []
            chapter_list_div = soup.select('.catalog-list ul li a')
            
            for link in chapter_list_div:
                chapter_url = urljoin('https://b.faloo.com', link['href'])
                chapter_title = link.text.strip()
                
                # 判断是否为付费章节
                is_vip = '(VIP)' in chapter_title or '[VIP]' in chapter_title or link.get('class') and 'vip' in ' '.join(link.get('class', []))
                
                chapter_links.append({
                    'title': chapter_title,
                    'url': chapter_url,
                    'is_vip': is_vip
                })
            
            logger.info(f"总共获取到 {len(chapter_links)} 个章节")
            return chapter_links
            
        except Exception as e:
            logger.error(f"获取章节列表时出错: {str(e)}")
            return []
    
    def get_chapter_content(self, chapter_url, is_vip=False, retry=3):
        """
        获取章节内容
        :param chapter_url: 章节URL
        :param is_vip: 是否为VIP章节
        :param retry: 重试次数
        """
        if is_vip and not self.is_logged_in:
            logger.warning("需要登录才能获取VIP章节内容")
            return "【需要登录才能获取VIP章节内容】"
        
        for i in range(retry):
            try:
                # 添加随机延迟，避免请求过于频繁
                time.sleep(random.uniform(1, 3))
                
                response = self.session.get(chapter_url)
                response.encoding = 'utf-8'
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 检查是否为付费章节
                if "购买本章" in response.text or "订阅本章" in response.text:
                    if not is_vip:
                        logger.warning(f"章节 {chapter_url} 为付费章节，但未标记为VIP")
                    
                    if not self.is_logged_in:
                        return "【付费章节，请登录后下载】"
                    
                    # 尝试获取付费章节内容
                    # 这里需要实现获取付费章节的逻辑，可能涉及到购买或使用特殊API
                    # 由于涉及付费，此处仅作示例，不实际实现
                    logger.info("尝试获取付费章节内容")
                    
                    # 从页面获取chapter_id和novel_id
                    chapter_id_match = re.search(r'var readChapterId\s*=\s*(\d+)', response.text)
                    novel_id_match = re.search(r'var novelId\s*=\s*(\d+)', response.text)
                    
                    if chapter_id_match and novel_id_match:
                        chapter_id = chapter_id_match.group(1)
                        novel_id = novel_id_match.group(1)
                        
                        # 构建API请求
                        api_url = f"https://b.faloo.com/ajax/ReadChapter.aspx"
                        params = {
                            "nid": novel_id,
                            "cid": chapter_id,
                            "rid": "0"
                        }
                        
                        api_response = self.session.get(api_url, params=params)
                        try:
                            json_data = json.loads(api_response.text)
                            if json_data.get('code') == 0 and json_data.get('data'):
                                content_html = json_data['data'].get('content', '')
                                soup_content = BeautifulSoup(content_html, 'html.parser')
                                return soup_content.get_text('\n\n')
                        except:
                            pass
                    
                    return "【付费章节，无法获取内容】"
                
                # 一般章节内容获取
                content_div = soup.select_one('#content')
                if content_div:
                    # 去除广告和不需要的元素
                    for ad in content_div.select('.bd_ad'):
                        ad.decompose()
                    
                    content = content_div.get_text('\n\n')
                    # 清理文本
                    content = re.sub(r'\n{3,}', '\n\n', content)
                    content = content.strip()
                    return content
                
                logger.warning(f"未能从章节页面获取内容，将重试 ({i+1}/{retry})")
            
            except Exception as e:
                logger.error(f"获取章节内容时出错: {str(e)}, 将重试 ({i+1}/{retry})")
        
        return "【获取章节内容失败】"
    
    def download_novel(self, novel_url, output_dir=None, start_chapter=1, end_chapter=None):
        """
        下载小说
        :param novel_url: 小说URL
        :param output_dir: 输出目录
        :param start_chapter: 起始章节索引（从1开始）
        :param end_chapter: 结束章节索引（如果为None则下载全部）
        """
        # 获取小说信息
        novel_info = self.get_novel_info(novel_url)
        if not novel_info:
            logger.error("无法获取小说信息，下载失败")
            return False
        
        # 获取章节列表
        chapters = self.get_chapter_list(novel_url)
        if not chapters:
            logger.error("无法获取章节列表，下载失败")
            return False
        
        # 创建输出目录
        novel_title = novel_info['title']
        if not output_dir:
            output_dir = f"./novels/{novel_title}"
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存小说信息
        with open(os.path.join(output_dir, 'info.json'), 'w', encoding='utf-8') as f:
            json.dump(novel_info, f, ensure_ascii=False, indent=4)
        
        # 下载小说封面
        if novel_info['cover_url']:
            try:
                cover_response = self.session.get(novel_info['cover_url'])
                with open(os.path.join(output_dir, 'cover.jpg'), 'wb') as f:
                    f.write(cover_response.content)
                logger.info("已下载小说封面")
            except Exception as e:
                logger.error(f"下载封面时出错: {str(e)}")
        
        # 调整章节范围
        if start_chapter < 1:
            start_chapter = 1
        
        if end_chapter is None or end_chapter > len(chapters):
            end_chapter = len(chapters)
        
        # 创建小说内容文件
        txt_filename = os.path.join(output_dir, f"{novel_title}.txt")
        with open(txt_filename, 'w', encoding='utf-8') as txt_file:
            # 写入小说信息
            txt_file.write(f"{novel_title}\n")
            txt_file.write(f"作者: {novel_info['author']}\n\n")
            txt_file.write(f"简介:\n{novel_info['intro']}\n\n")
            txt_file.write("-" * 50 + "\n\n")
            
            # 下载每个章节
            total_chapters = end_chapter - start_chapter + 1
            for i, chapter in enumerate(chapters[start_chapter-1:end_chapter], 1):
                chapter_title = chapter['title']
                chapter_url = chapter['url']
                is_vip = chapter['is_vip']
                
                logger.info(f"正在下载: [{i}/{total_chapters}] {chapter_title}")
                
                # 获取章节内容
                content = self.get_chapter_content(chapter_url, is_vip)
                
                # 写入章节到文件
                txt_file.write(f"{chapter_title}\n\n")
                txt_file.write(f"{content}\n\n")
                txt_file.write("-" * 30 + "\n\n")
                
                # 每下载10章保存一次
                if i % 10 == 0:
                    txt_file.flush()
                    logger.info(f"已完成: {i}/{total_chapters} 章节")
        
        logger.info(f"小说《{novel_title}》下载完成！保存路径: {txt_filename}")
        return True


if __name__ == "__main__":
    # 使用示例
    print("飞卢小说下载器")
    print("=" * 50)
    
    # 输入账号密码（可选）
    use_account = input("是否使用账号登录下载付费内容？(y/n): ").strip().lower()
    username = None
    password = None
    
    if use_account == 'y':
        username = input("请输入账号: ").strip()
        password = input("请输入密码: ").strip()
    
    # 创建下载器
    downloader = FeiluNovelDownloader(username, password)
    
    # 如果提供了账号密码，尝试登录
    if username and password:
        if not downloader.login():
            print("登录失败，将以游客身份继续")
    
    # 输入小说网址
    novel_url = input("请输入小说链接(例如:https://b.faloo.com/novel/123456.html): ").strip()
    
    # 章节范围（可选）
    range_option = input("下载全部章节？(y/n, 默认y): ").strip().lower()
    start_chapter = 1
    end_chapter = None
    
    if range_option == 'n':
        try:
            start_input = input("请输入起始章节序号(默认1): ").strip()
            start_chapter = int(start_input) if start_input else 1
            
            end_input = input("请输入结束章节序号(留空表示到最后): ").strip()
            end_chapter = int(end_input) if end_input else None
        except ValueError:
            print("输入格式错误，将使用默认值")
            start_chapter = 1
            end_chapter = None
    
    # 输出目录（可选）
    output_dir = input("请输入保存目录(留空使用默认目录): ").strip()
    
    print("\n开始下载小说...")
    downloader.download_novel(novel_url, output_dir, start_chapter, end_chapter) 