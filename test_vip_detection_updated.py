#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from feilu_novel_downloader import FeiluNovelDownloader

def test_vip_detection():
    # 创建下载器实例（不登录）
    downloader = FeiluNovelDownloader()
    
    # 测试几个不同的章节
    test_chapters = [
        "https://b.faloo.com/1236995_1.html",    # 第1章 - 应该是免费的
        "https://b.faloo.com/1236995_100.html",  # 第100章 - 可能是VIP
        "https://b.faloo.com/1236995_500.html",  # 第500章 - 可能是VIP
    ]
    
    for chapter_url in test_chapters:
        print(f"\n{'='*60}")
        print(f"测试章节: {chapter_url}")
        
        try:
            content = downloader.get_chapter_content(chapter_url, is_vip=False)
            print(f"内容长度: {len(content)}")
            print(f"内容预览: {content[:200]}...")
            
            if "付费章节" in content:
                print("✅ 正确识别为付费章节")
            else:
                print("✅ 成功获取免费章节内容")
                
        except Exception as e:
            print(f"❌ 获取失败: {e}")

if __name__ == "__main__":
    test_vip_detection()
