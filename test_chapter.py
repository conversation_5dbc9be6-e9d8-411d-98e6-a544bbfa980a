import requests
from bs4 import BeautifulSoup
import time

def test_chapter_content():
    session = requests.Session()
    
    # 使用简单的headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    }
    session.headers.update(headers)
    
    # 先访问主页
    try:
        session.get('https://b.faloo.com/', timeout=10)
        time.sleep(1)
    except:
        pass
    
    # 测试章节URL
    chapter_url = "https://b.faloo.com/1236995_1.html"  # 第1章
    print(f"访问章节: {chapter_url}")
    
    try:
        response = session.get(chapter_url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        
        response.encoding = 'gbk'
        soup = BeautifulSoup(response.text, 'html.parser')
        
        title = soup.title.text if soup.title else '无标题'
        print(f"页面标题: {title}")
        
        # 查找内容区域
        content_selectors = [
            '#content',
            '.content',
            '#chaptercontent',
            '.chapter-content',
            '.read-content',
            '.txt'
        ]
        
        content_found = False
        for selector in content_selectors:
            content_div = soup.select_one(selector)
            if content_div:
                content = content_div.get_text()
                print(f"使用选择器 {selector} 找到内容:")
                print(f"内容长度: {len(content)}")
                print(f"内容预览: {content[:200]}...")
                content_found = True
                break
        
        if not content_found:
            print("未找到内容区域，查找所有可能的内容区域...")

            # 查找所有div
            all_divs = soup.find_all('div')
            for i, div in enumerate(all_divs):
                div_text = div.get_text().strip()
                if len(div_text) > 100 and '第1章' in div_text:
                    print(f"找到可能的内容div {i}: class={div.get('class')}, id={div.get('id')}")
                    print(f"内容预览: {div_text[:200]}...")
                    break

            # 查找包含章节内容的特定标签
            for tag in ['p', 'div', 'span']:
                elements = soup.find_all(tag)
                for elem in elements:
                    text = elem.get_text().strip()
                    if len(text) > 200 and any(keyword in text for keyword in ['穿越', '系统', '罗枢']):
                        print(f"找到可能的内容{tag}: class={elem.get('class')}, id={elem.get('id')}")
                        print(f"内容预览: {text[:300]}...")
                        break
        
    except Exception as e:
        print(f"访问失败: {e}")

if __name__ == "__main__":
    test_chapter_content()
