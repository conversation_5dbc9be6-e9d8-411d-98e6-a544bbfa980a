import requests
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin

def check_vip_detection():
    session = requests.Session()
    
    # 使用简单的headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    }
    session.headers.update(headers)
    
    # 先访问主页
    try:
        session.get('https://b.faloo.com/', timeout=10)
        time.sleep(1)
    except:
        pass
    
    # 获取小说主页
    novel_url = "https://b.faloo.com/1236995.html"
    response = session.get(novel_url, timeout=10)
    response.encoding = 'gbk'
    soup = BeautifulSoup(response.text, 'html.parser')
    
    all_links = soup.find_all('a', href=True)
    novel_id = novel_url.split('/')[-1].split('.')[0]
    
    print(f"小说ID: {novel_id}")
    print(f"总链接数: {len(all_links)}")
    
    chapter_links = []
    vip_count = 0
    free_count = 0
    
    for link in all_links:
        href = link['href']
        text = link.text.strip()
        
        # 检查是否是章节链接
        is_chapter_link = False
        
        if novel_id in href and '_' in href and '.html' in href:
            pattern = rf'{novel_id}_(\d+)\.html'
            if re.search(pattern, href):
                is_chapter_link = True
        
        if text and re.search(r'第\d+章', text):
            is_chapter_link = True
        
        if is_chapter_link:
            skip_keywords = [
                '目录', '返回', '更多', '作者', '书评', '推荐', '收藏', 
                '点击阅读', '我要订阅', '下载', '作品相关', '感言',
                '上一章', '下一章', '章节目录', '加入书签', '第一卷', '第二卷',
                '第三卷', '第四卷', '第五卷', '第六卷', '第七卷', '第八卷', '第九卷',
                '承诺书', '卷', '初始', '厄难使者', '灾厄时代', '深渊魔境'
            ]
            
            if '卷' in text and '第' in text and '章' not in text:
                continue
            
            if not any(skip in text for skip in skip_keywords) and len(text) > 2:
                chapter_url = urljoin('https://b.faloo.com', href)
                
                # 检查VIP标识
                is_vip = False
                
                # 方法1: 检查文本中的VIP标识
                if '(VIP)' in text or '[VIP]' in text:
                    is_vip = True
                
                # 方法2: 检查链接的class属性
                if link.get('class') and 'vip' in ' '.join(link.get('class', [])):
                    is_vip = True
                
                # 方法3: 检查链接的其他属性
                if link.get('style') and 'color' in link.get('style', ''):
                    # VIP章节可能有特殊颜色
                    is_vip = True
                
                chapter_links.append({
                    'title': text,
                    'url': chapter_url,
                    'is_vip': is_vip,
                    'class': link.get('class'),
                    'style': link.get('style')
                })
                
                if is_vip:
                    vip_count += 1
                else:
                    free_count += 1
    
    print(f"\n总章节数: {len(chapter_links)}")
    print(f"免费章节: {free_count}")
    print(f"VIP章节: {vip_count}")
    
    print(f"\n前20个章节的VIP状态:")
    for i, chapter in enumerate(chapter_links[:20]):
        vip_status = "VIP" if chapter['is_vip'] else "免费"
        print(f"{i+1:2d}. {vip_status} - {chapter['title']}")
        if chapter['class'] or chapter['style']:
            print(f"    class: {chapter['class']}, style: {chapter['style']}")
    
    print(f"\n后20个章节的VIP状态:")
    for i, chapter in enumerate(chapter_links[-20:], len(chapter_links)-19):
        vip_status = "VIP" if chapter['is_vip'] else "免费"
        print(f"{i:2d}. {vip_status} - {chapter['title']}")
        if chapter['class'] or chapter['style']:
            print(f"    class: {chapter['class']}, style: {chapter['style']}")

if __name__ == "__main__":
    check_vip_detection()
