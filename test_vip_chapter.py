import requests
from bs4 import BeautifulSoup
import time

def test_vip_chapter():
    session = requests.Session()
    
    # 使用简单的headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    }
    session.headers.update(headers)
    
    # 先访问主页
    try:
        session.get('https://b.faloo.com/', timeout=10)
        time.sleep(1)
    except:
        pass
    
    # 测试一个可能的VIP章节URL
    # 从之前的输出看，章节编号比较大的可能是VIP章节
    test_urls = [
        "https://b.faloo.com/1236995_100.html",  # 第100章
        "https://b.faloo.com/1236995_500.html",  # 第500章
        "https://b.faloo.com/1236995_1000.html", # 第1000章
        "https://b.faloo.com/1236995_2000.html", # 第2000章
    ]
    
    for chapter_url in test_urls:
        print(f"\n{'='*60}")
        print(f"测试章节: {chapter_url}")
        
        try:
            response = session.get(chapter_url, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code != 200:
                print("章节不存在或无法访问")
                continue
            
            response.encoding = 'gbk'
            soup = BeautifulSoup(response.text, 'html.parser')
            
            title = soup.title.text if soup.title else '无标题'
            print(f"页面标题: {title}")
            
            # 检查是否为付费章节的关键词
            vip_keywords = ['购买本章', '订阅本章', 'VIP', '付费', '需要订阅', '本章需要']
            is_vip = False
            for keyword in vip_keywords:
                if keyword in response.text:
                    print(f"发现VIP关键词: {keyword}")
                    is_vip = True
            
            if not is_vip:
                print("未发现VIP标识，可能是免费章节")
            
            # 查找内容区域
            content_div = soup.select_one('#center')
            if content_div:
                content = content_div.get_text()
                print(f"内容长度: {len(content)}")
                print(f"内容预览: {content[:300]}...")
            else:
                print("未找到内容区域")
                # 显示页面的一部分内容来分析
                print("页面内容预览:")
                print(response.text[:1000])
            
        except Exception as e:
            print(f"访问失败: {e}")

if __name__ == "__main__":
    test_vip_chapter()
